"""
文件: xlsx2json.py
功能: Excel和JSON文件互相转换
描述: 该脚本可以将Excel文件转换为JSON格式，也可以将JSON文件转换为Excel格式。
      主要功能包括：
      1. 读取Excel文件并转换为JSON格式
      2. 读取JSON文件并转换为Excel格式
      3. 处理数据中的空值(NaN)
      4. 基于"案号"字段去除重复记录
      5. 自动检测输入文件格式并选择相应的转换方式
"""
import pandas as pd
import json
import sys
import os
from pathlib import Path

def xlsx_to_json(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    将Excel文件转换为JSON格式
    
    参数:
        file_path: Excel文件路径
        output_file: 输出的JSON文件路径，如不指定则使用Excel文件名
        encoding: 文件编码格式
        sample_num: 如指定，则只转换前N条记录
        
    返回:
        输出的JSON文件路径
    """
    print(f"正在读取Excel文件: {file_path}")
    
    if sample_num:
        df = pd.read_excel(file_path).head(sample_num)
    else:
        df = pd.read_excel(file_path)

    print(f"成功读取数据，共 {len(df)} 行")
    
    # 将NaN值替换为None
    df = df.where(pd.notnull(df), None)
    
    # 检查是否有"案号"列，执行去重前统计重复案号
    if "案号" in df.columns:
        # 统计案号出现次数
        counts = df["案号"].value_counts()
        duplicates = counts[counts > 1]
        if not duplicates.empty:
            print("发现以下重复案号及其出现次数：")
            for case_id, count in duplicates.items():
                print(f"案号: {case_id}，出现次数: {count}")
        else:
            print("未发现重复案号。")
        original_count = len(df)
        # 去除重复的"案号"记录，保留第一次出现的记录
        df = df.drop_duplicates(subset=["案号"], keep="first")
        removed_count = original_count - len(df)
        if removed_count > 0:
            print(f"已去除 {removed_count} 条重复案号的记录，剩余 {len(df)} 条记录")
    
    # 转换为字典列表
    data = df.to_dict('records')

    # 递归处理所有NaN值
    def replace_nan(obj):
        if isinstance(obj, float) and pd.isna(obj):
            return None
        elif isinstance(obj, dict):
            return {k: replace_nan(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [replace_nan(i) for i in obj]
        else:
            return obj

    data = replace_nan(data)

    # 生成输出文件名
    if not output_file:
        path = Path(file_path)
        output_file = "/home/<USER>/Xinwu/data_PCV.json"

    # 保存为JSON文件
    print(f"正在保存为JSON文件: {output_file}")
    with open(output_file, 'w', encoding=encoding) as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"转换完成！JSON文件已保存为: {output_file}")
    return output_file


def json_to_xlsx(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    将JSON文件转换为Excel格式

    参数:
        file_path: JSON文件路径
        output_file: 输出的Excel文件路径，如不指定则使用JSON文件名
        encoding: 文件编码格式
        sample_num: 如指定，则只转换前N条记录

    返回:
        输出的Excel文件路径
    """
    print(f"正在读取JSON文件: {file_path}")

    try:
        with open(file_path, 'r', encoding=encoding) as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式不正确: {e}")
        return None
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return None
    except Exception as e:
        print(f"错误：读取文件时发生错误: {e}")
        return None

    # 确保数据是列表格式
    if isinstance(data, dict):
        data = [data]
    elif not isinstance(data, list):
        print("错误：JSON数据应为对象或对象数组格式")
        return None

    if not data:
        print("警告：JSON文件为空")
        return None

    print(f"成功读取数据，共 {len(data)} 条记录")

    # 如果指定了样本数量，则只取前N条
    if sample_num and sample_num < len(data):
        data = data[:sample_num]
        print(f"已限制为前 {sample_num} 条记录")

    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 检查是否有"案号"列，执行去重前统计重复案号
    if "案号" in df.columns:
        # 统计案号出现次数
        counts = df["案号"].value_counts()
        duplicates = counts[counts > 1]
        if not duplicates.empty:
            print("发现以下重复案号及其出现次数：")
            for case_id, count in duplicates.items():
                print(f"案号: {case_id}，出现次数: {count}")
        else:
            print("未发现重复案号。")
        original_count = len(df)
        # 去除重复的"案号"记录，保留第一次出现的记录
        df = df.drop_duplicates(subset=["案号"], keep="first")
        removed_count = original_count - len(df)
        if removed_count > 0:
            print(f"已去除 {removed_count} 条重复案号的记录，剩余 {len(df)} 条记录")

    # 生成输出文件名
    if not output_file:
        path = Path(file_path)
        output_file = path.with_suffix('.xlsx')

    # 保存为Excel文件
    print(f"正在保存为Excel文件: {output_file}")
    try:
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"转换完成！Excel文件已保存为: {output_file}")
        return output_file
    except Exception as e:
        print(f"错误：保存Excel文件时发生错误: {e}")
        return None


def detect_file_type(file_path):
    """
    检测文件类型

    参数:
        file_path: 文件路径

    返回:
        文件类型: 'xlsx', 'json', 或 'unknown'
    """
    path = Path(file_path)
    suffix = path.suffix.lower()

    if suffix in ['.xlsx', '.xls']:
        return 'xlsx'
    elif suffix == '.json':
        return 'json'
    else:
        return 'unknown'


def convert_file(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    自动检测文件类型并进行相应转换

    参数:
        file_path: 输入文件路径
        output_file: 输出文件路径，如不指定则自动生成
        encoding: 文件编码格式
        sample_num: 如指定，则只转换前N条记录

    返回:
        输出文件路径，转换失败返回None
    """
    if not Path(file_path).exists():
        print(f"错误：文件 {file_path} 不存在")
        return None

    file_type = detect_file_type(file_path)

    if file_type == 'xlsx':
        print("检测到Excel文件，将转换为JSON格式")
        return xlsx_to_json(file_path, output_file, encoding, sample_num)
    elif file_type == 'json':
        print("检测到JSON文件，将转换为Excel格式")
        return json_to_xlsx(file_path, output_file, encoding, sample_num)
    else:
        print(f"错误：不支持的文件类型。文件: {file_path}")
        print("支持的文件类型: .xlsx, .xls, .json")
        return None


def main():
    """主函数，处理命令行参数"""
    if len(sys.argv) < 2:
        print("用法: python xlsx2json.py <输入文件> [输出文件] [--sample N] [--encoding 编码]")
        print("示例:")
        print("  python xlsx2json.py data.xlsx")
        print("  python xlsx2json.py data.json output.xlsx")
        print("  python xlsx2json.py data.xlsx --sample 100")
        print("  python xlsx2json.py data.json --encoding gbk")
        return

    # 解析命令行参数
    input_file = sys.argv[1]
    output_file = None
    encoding = 'utf-8'
    sample_num = None

    i = 2
    while i < len(sys.argv):
        if sys.argv[i] == '--sample' and i + 1 < len(sys.argv):
            try:
                sample_num = int(sys.argv[i + 1])
                i += 2
            except ValueError:
                print("错误：--sample 参数必须是数字")
                return
        elif sys.argv[i] == '--encoding' and i + 1 < len(sys.argv):
            encoding = sys.argv[i + 1]
            i += 2
        elif not output_file and not sys.argv[i].startswith('--'):
            output_file = sys.argv[i]
            i += 1
        else:
            print(f"错误：未知参数 {sys.argv[i]}")
            return

    # 执行转换
    result = convert_file(input_file, output_file, encoding, sample_num)
    if result:
        print(f"\n转换成功！输出文件: {result}")
    else:
        print("\n转换失败！")


if __name__ == "__main__":
    # 如果没有命令行参数，使用默认行为（保持向后兼容）
    if len(sys.argv) == 1:
        file_path = "/home/<USER>/Xinwu/lathan/data_PCV.xlsx"
        xlsx_to_json(file_path)
    else:
        main()
