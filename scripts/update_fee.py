import json
from typing import Optional

SOURCE_DATA_1 = "ds_retry_6260.json"
SOURCE_DATA_2 = "4.1_retry_6260.json"

TARGET_DATA_1 = "ds_statistic_fee.json"
TARGET_DATA_2 = "4.1_statistic_fee.json"


def is_consistent(value1: Optional[float], value2: Optional[float], tolerance: float = 1000.0) -> bool:
    """
    判断两个金额是否一致，允许有一定的误差范围
    
    Args:
        value1: 第一个金额
        value2: 第二个金额
        tolerance: 允许的误差范围，默认为10元
        
    Returns:
        bool: 如果两个金额在误差范围内一致，返回True；否则返回False
    """
    if value1 is None and value2 is None:
        return True
    if value1 is None or value2 is None:
        return False
    
    tolerance = max(value1, value2) * 0.05
    return abs(value1 - value2) <= tolerance

def update_json_field():
    # 读取所有四个文件
    with open(SOURCE_DATA_1, "r", encoding="utf-8") as f:
        source_data_1 = json.load(f)

    with open(SOURCE_DATA_2, "r", encoding="utf-8") as f:
        source_data_2 = json.load(f)

    with open(TARGET_DATA_1, "r", encoding="utf-8") as f:
        target_data_1 = json.load(f)

    with open(TARGET_DATA_2, "r", encoding="utf-8") as f:
        target_data_2 = json.load(f)

    # 创建案号映射表
    source_map_1 = {}
    for item in source_data_1:
        if "案号" in item:
            case_number = item["案号"]
            source_map_1[case_number] = item

    source_map_2 = {}
    for item in source_data_2:
        if "案号" in item:
            case_number = item["案号"]
            source_map_2[case_number] = item

    target_map_1 = {}
    for item in target_data_1:
        if "案号" in item:
            case_number = item["案号"]
            target_map_1[case_number] = item

    target_map_2 = {}
    for item in target_data_2:
        if "案号" in item:
            case_number = item["案号"]
            target_map_2[case_number] = item

    # 检查SOURCE_DATA_1和SOURCE_DATA_2的案号是否一一对应
    source_1_cases = set(source_map_1.keys())
    source_2_cases = set(source_map_2.keys())

    if source_1_cases != source_2_cases:
        print("警告: SOURCE_DATA_1 和 SOURCE_DATA_2 的案号不完全一致!")
        only_in_1 = source_1_cases - source_2_cases
        only_in_2 = source_2_cases - source_1_cases
        if only_in_1:
            print(f"只在 SOURCE_DATA_1 中的案号数量: {len(only_in_1)}")
        if only_in_2:
            print(f"只在 SOURCE_DATA_2 中的案号数量: {len(only_in_2)}")
    else:
        print("✓ SOURCE_DATA_1 和 SOURCE_DATA_2 的案号完全一致")

    # 获取共同的案号
    common_cases = source_1_cases & source_2_cases
    print(f"共同案号数量: {len(common_cases)}")

    # 统计变量
    updated_count_1 = 0
    updated_count_2 = 0
    plaintiff_updated_count_1 = 0
    judgment_updated_count_1 = 0
    plaintiff_updated_count_2 = 0
    judgment_updated_count_2 = 0
    updated_cases_1 = []
    updated_cases_2 = []

    # 处理所有条目的更新（一次性判断，同时更新两个目标文件）
    for case_number in common_cases:
        if case_number in target_map_1 and case_number in target_map_2:
            source_item_1 = source_map_1[case_number]
            source_item_2 = source_map_2[case_number]
            target_item_1 = target_map_1[case_number]
            target_item_2 = target_map_2[case_number]

            # 获取TARGET_DATA_1和TARGET_DATA_2中的总金额
            target_1_plaintiff_total = target_item_1.get("原告要求赔偿金额", {}).get("总金额") if isinstance(target_item_1.get("原告要求赔偿金额"), dict) else None
            target_1_judgment_total = target_item_1.get("最终判决赔偿金额", {}).get("总金额") if isinstance(target_item_1.get("最终判决赔偿金额"), dict) else None

            target_2_plaintiff_total = target_item_2.get("原告要求赔偿金额", {}).get("总金额") if isinstance(target_item_2.get("原告要求赔偿金额"), dict) else None
            target_2_judgment_total = target_item_2.get("最终判决赔偿金额", {}).get("总金额") if isinstance(target_item_2.get("最终判决赔偿金额"), dict) else None

            # 标记是否有更新
            case_updated_1 = False
            case_updated_2 = False

            # 检查原告要求赔偿金额是否需要更新
            if not is_consistent(target_1_plaintiff_total, target_2_plaintiff_total):
                # 同时更新两个目标文件
                target_item_1["原告要求赔偿金额"] = source_item_1.get("原告要求赔偿金额", {})
                target_item_2["原告要求赔偿金额"] = source_item_2.get("原告要求赔偿金额", {})
                plaintiff_updated_count_1 += 1
                plaintiff_updated_count_2 += 1
                case_updated_1 = True
                case_updated_2 = True

            # 检查最终判决赔偿金额是否需要更新
            if not is_consistent(target_1_judgment_total, target_2_judgment_total):
                # 同时更新两个目标文件
                target_item_1["最终判决赔偿金额"] = source_item_1.get("最终判决赔偿金额", {})
                target_item_2["最终判决赔偿金额"] = source_item_2.get("最终判决赔偿金额", {})
                judgment_updated_count_1 += 1
                judgment_updated_count_2 += 1
                case_updated_1 = True
                case_updated_2 = True

            # 记录更新的案号
            if case_updated_1:
                updated_count_1 += 1
                updated_cases_1.append(case_number)
            if case_updated_2:
                updated_count_2 += 1
                updated_cases_2.append(case_number)

    # 保存更新后的目标文件
    with open(TARGET_DATA_1, "w", encoding="utf-8") as f:
        json.dump(target_data_1, f, ensure_ascii=False, indent=4)

    with open(TARGET_DATA_2, "w", encoding="utf-8") as f:
        json.dump(target_data_2, f, ensure_ascii=False, indent=4)

    # 输出更新结果
    print("\n=== TARGET_DATA_1 更新结果 ===")
    print(f"总共更新了 {updated_count_1} 个条目")
    print(f"其中原告要求赔偿金额更新了 {plaintiff_updated_count_1} 个条目")
    print(f"其中最终判决赔偿金额更新了 {judgment_updated_count_1} 个条目")

    print("\n=== TARGET_DATA_2 更新结果 ===")
    print(f"总共更新了 {updated_count_2} 个条目")
    print(f"其中原告要求赔偿金额更新了 {plaintiff_updated_count_2} 个条目")
    print(f"其中最终判决赔偿金额更新了 {judgment_updated_count_2} 个条目")

    # print("更新的案号列表:")
    # for case in updated_cases:
    #     print(case)

    return {
        "target_1": {
            "updated_count": updated_count_1,
            "plaintiff_updated": plaintiff_updated_count_1,
            "judgment_updated": judgment_updated_count_1,
            "updated_cases": updated_cases_1
        },
        "target_2": {
            "updated_count": updated_count_2,
            "plaintiff_updated": plaintiff_updated_count_2,
            "judgment_updated": judgment_updated_count_2,
            "updated_cases": updated_cases_2
        }
    }

if __name__ == "__main__":
    update_json_field()
