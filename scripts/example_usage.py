#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from merge_json import merge_json_files

def create_test_files():
    """创建测试用的JSON文件"""
    
    # 测试文件1：基本案件信息
    file1_data = [
        {
            "案号": "2023-001",
            "案件名称": "张三诉李四合同纠纷案",
            "案件类型": "民事案件",
            "立案时间": "2023-01-15"
        },
        {
            "案号": "2023-002", 
            "案件名称": "王五交通事故案",
            "案件类型": "民事案件",
            "立案时间": "2023-02-20"
        }
    ]
    
    # 测试文件2：审理信息
    file2_data = [
        {
            "案号": "2023-001",
            "审理法官": "赵法官",
            "开庭时间": "2023-03-10",
            "审理状态": "审理中"
        },
        {
            "案号": "2023-003",
            "审理法官": "钱法官", 
            "开庭时间": "2023-03-15",
            "审理状态": "待审理"
        }
    ]
    
    # 测试文件3：包含冲突的数据
    file3_data = [
        {
            "案号": "2023-001",
            "案件类型": "刑事案件",  # 与文件1冲突
            "结案时间": "2023-04-01"
        }
    ]
    
    # 保存测试文件
    with open('test1.json', 'w', encoding='utf-8') as f:
        json.dump(file1_data, f, ensure_ascii=False, indent=2)
    
    with open('test2.json', 'w', encoding='utf-8') as f:
        json.dump(file2_data, f, ensure_ascii=False, indent=2)
        
    with open('test3.json', 'w', encoding='utf-8') as f:
        json.dump(file3_data, f, ensure_ascii=False, indent=2)

def test_successful_merge():
    """测试成功合并的情况"""
    print("=== 测试成功合并 ===")
    result = merge_json_files(['test1.json', 'test2.json'], 'merged_success.json')
    if result:
        print("合并成功！")
        for record in result:
            print(f"案号: {record['案号']}, 字段数: {len(record)}")

def test_conflict_detection():
    """测试冲突检测"""
    print("\n=== 测试冲突检测 ===")
    result = merge_json_files(['test1.json', 'test3.json'], 'merged_conflict.json')
    if result is None:
        print("正确检测到冲突！")

if __name__ == "__main__":
    # 创建测试文件
    create_test_files()
    print("已创建测试文件: test1.json, test2.json, test3.json")
    
    # 运行测试
    test_successful_merge()
    test_conflict_detection()
