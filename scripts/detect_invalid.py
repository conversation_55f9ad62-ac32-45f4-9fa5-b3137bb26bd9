#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测 ds_statistic.json 中是否有不符合要求格式的条例
"""

JSON_FILE = "/home/<USER>/Xinwu/ds_statistic_final.json"
OUTPUT_FILE = "/home/<USER>/Xinwu/invalid_ds_cases.txt"

import json
from typing import Dict, List, Any

def validate_case_format(case: Dict[str, Any]) -> List[str]:
    """
    验证单个案例是否符合要求格式

    Args:
        case: 案例数据字典

    Returns:
        错误信息列表，如果为空则表示格式正确
    """
    errors = []

    # 必须存在的字段列表
    required_fields = [
        "案号",
        "法院信息_省",
        "法院信息_市",
        "法院信息_县",
        "法院等级",
        "原告",
        "被告",
        "第三人",
        "上诉人",
        "被上诉人",
        "审判长",
        "审判员",
        "原告要求赔偿金额",
        "最终判决赔偿金额",
        "案件简述"
    ]

    # 检查必须字段是否存在
    for field in required_fields:
        if field not in case:
            errors.append(f"缺少必须字段: {field}")

    # 检查是否有多余字段
    for field in case.keys():
        if field not in required_fields:
            errors.append(f"存在多余字段: {field}")

    # 检查案号字段不能为null（其他字段可以为null）
    if "案号" in case:
        if case["案号"] is None or case["案号"] == "null" or case["案号"] == "":
            errors.append("案号字段不能为null或空字符串")

    # 检查赔偿金额字段的格式
    for amount_field in ["原告要求赔偿金额", "最终判决赔偿金额"]:
        if amount_field in case:
            amount_data = case[amount_field]
            if not isinstance(amount_data, dict):
                errors.append(f"{amount_field}必须是字典格式")
            else:
                # 检查是否包含总金额字段
                if "总金额" not in amount_data:
                    errors.append(f"{amount_field}缺少总金额字段")

    return errors

def detect_invalid_cases(json_file_path: str) -> tuple[List[Dict], List[str]]:
    """
    检测JSON文件中的无效案例

    Args:
        json_file_path: JSON文件路径

    Returns:
        (invalid_cases, invalid_case_numbers): 无效案例列表和案号列表
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        return [], []
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式错误 - {e}")
        return [], []

    if not isinstance(data, list):
        print("错误: JSON文件根元素必须是数组")
        return [], []

    invalid_cases = []
    invalid_case_numbers = []

    print(f"开始检测 {len(data)} 个案例...")
    print("=" * 80)

    for i, case in enumerate(data):
        if not isinstance(case, dict):
            error_info = {
                "index": i,
                "case_number": "未知",
                "errors": ["案例数据必须是字典格式"]
            }
            invalid_cases.append(error_info)
            invalid_case_numbers.append("未知")
            print(f"案例 {i+1}: 数据格式错误 - 不是字典格式")
            continue

        errors = validate_case_format(case)

        if errors:
            case_number = case.get("案号", "未知案号")
            error_info = {
                "index": i,
                "case_number": case_number,
                "errors": errors
            }
            invalid_cases.append(error_info)
            invalid_case_numbers.append(case_number)

            print(f"案例 {i+1} (案号: {case_number}):")
            for error in errors:
                print(f"  - {error}")
            print()

    return invalid_cases, invalid_case_numbers

def main():
    """主函数"""
    # 文件路径
    json_file = JSON_FILE
    output_file = OUTPUT_FILE

    print("开始检测 ds_statistic.json 中的无效案例...")
    print(f"输入文件: {json_file}")
    print(f"输出文件: {output_file}")
    print()

    # 检测无效案例
    invalid_cases, invalid_case_numbers = detect_invalid_cases(json_file)

    # 输出统计结果
    print("=" * 80)
    print("检测完成!")
    print(f"总共检测案例数: {len(invalid_cases) if invalid_cases else '文件读取失败'}")
    print(f"发现无效案例数: {len(invalid_cases)}")

    if invalid_cases:
        print("\n无效案例详细报告:")
        print("=" * 80)

        for case_info in invalid_cases:
            print(f"案例索引: {case_info['index']+1}")
            print(f"案号: {case_info['case_number']}")
            print("错误信息:")
            for error in case_info['errors']:
                print(f"  - {error}")
            print("-" * 40)

        # 将无效案号写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("无效案例的案号列表:\n")
                f.write("=" * 50 + "\n")
                for case_number in invalid_cases:
                    f.write(f"{case_number['case_number']}\n")

            print(f"\n无效案号已输出到文件: {output_file}")

        except Exception as e:
            print(f"写入文件时出错: {e}")

    else:
        print("恭喜! 所有案例都符合要求格式。")
        # 创建空的输出文件表示没有无效案例
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("检测结果: 所有案例都符合要求格式，没有发现无效案例。\n")
            print(f"检测结果已输出到文件: {output_file}")
        except Exception as e:
            print(f"写入文件时出错: {e}")

if __name__ == "__main__":
    main()