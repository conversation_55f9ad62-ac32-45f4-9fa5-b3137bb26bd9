import json
import os
import sys

def process_json_file(file_path):
    """处理单个JSON文件，提取总金额并创建新字段"""
    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查并提取原告要求赔偿金额的总金额
        if "原告要求赔偿金额" in data and "总金额" in data["原告要求赔偿金额"]:
            data["原告要求总金额"] = data["原告要求赔偿金额"]["总金额"]
        
        # 检查并提取最终判决赔偿金额的总金额
        if "最终判决赔偿金额" in data and "总金额" in data["最终判决赔偿金额"]:
            data["最终判决总金额"] = data["最终判决赔偿金额"]["总金额"]
        
        # 保存修改后的JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"成功处理文件: {file_path}")
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")

def process_directory(directory_path):
    """处理目录中的所有JSON文件"""
    for filename in os.listdir(directory_path):
        if filename.endswith('.json'):
            file_path = os.path.join(directory_path, filename)
            process_json_file(file_path)

def main():
    if len(sys.argv) < 2:
        print("用法: python tmp.py <JSON文件路径或目录路径>")
        sys.exit(1)
    
    input_path = sys.argv[1]
    
    if os.path.isfile(input_path) and input_path.endswith('.json'):
        # 处理单个JSON文件
        process_json_file(input_path)
    elif os.path.isdir(input_path):
        # 处理目录中的所有JSON文件
        process_directory(input_path)
    else:
        print("错误: 请提供有效的JSON文件路径或包含JSON文件的目录路径")

if __name__ == "__main__":
    main()
